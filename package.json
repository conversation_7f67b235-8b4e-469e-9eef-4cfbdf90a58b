{"name": "supasmoothies", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.8.2", "react-scripts": "^5.0.1", "web-vitals": "^5.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.42.0", "@typescript-eslint/parser": "^8.42.0", "eslint": "^8.57.0", "eslint-plugin-jest": "^29.0.1", "eslint-webpack-plugin": "^5.0.2"}}